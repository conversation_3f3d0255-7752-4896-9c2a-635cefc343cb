name: Checks

on:
  push:
    branches:
    - main
  pull_request:
    branches:
    - main

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11', '3.12', '3.13']

    steps:
    - uses: actions/checkout@v4
    - name: Install uv with Python ${{ matrix.python-version }}
      uses: astral-sh/setup-uv@v5
      with:
        version: "0.6.6"
        python-version: ${{ matrix.python-version }}
        enable-cache: true
    - name: Install dependencies
      run: uv tool install tox --with tox-uv --with tox-gh-actions
    - name: Test with tox
      run: tox

  type-check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Install uv
      uses: astral-sh/setup-uv@v5
      with:
        version: "0.6.6"
        python-version: '3.12'
        enable-cache: true
    - run: uv sync
    - run: echo "$(uv run echo $VIRTUAL_ENV)/bin" >> $GITHUB_PATH
    - name: Pyright
      uses: jakebailey/pyright-action@v2
      with:
        pylance-version: latest-release
    - name: Mypy
      run: mypy .
