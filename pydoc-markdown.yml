loaders:
- type: python
  search_path: [.]
  modules: [parmancer]

processors:
- type: filter
  # Keep only the module docstring, exclude all members
  expression: "not hasattr(obj, 'parent') or obj.parent is None"
  documented_only: false
  exclude_private: false
  exclude_special: false
  do_not_filter_modules: true
  skip_empty_modules: false

renderer:
  type: markdown
  render_module_header: false
  render_toc: false
  descriptive_class_title: false
  add_method_class_prefix: false
  add_member_class_prefix: false
